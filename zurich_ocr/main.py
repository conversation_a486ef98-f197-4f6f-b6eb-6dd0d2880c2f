"""
Zurich OCR Engine - Clean and Simple Implementation
"""

import asyncio
import json
import time
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
import io

import uvicorn
from fastapi import FastAPI, File, Form, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

# Core processing libraries
import pytesseract
from PIL import Image
import pdfplumber
import pandas as pd
from docx import Document
import openpyxl
from bs4 import BeautifulSoup

# Initialize FastAPI app
app = FastAPI(
    title="Zurich OCR Engine",
    description="Clean OCR and document processing API",
    version="2.0.0",
    docs_url="/docs"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def clean_text(text: str) -> str:
    """Clean extracted text"""
    if not text:
        return ""
    return text.strip().replace('\x00', '').replace('\r\n', '\n')

def detect_file_type(filename: str) -> str:
    """Simple file type detection"""
    if not filename or '.' not in filename:
        return 'unknown'
    return filename.lower().split('.')[-1]

def process_text_file(file_data: bytes) -> str:
    """Process text file"""
    try:
        return file_data.decode('utf-8', errors='replace')
    except Exception as e:
        return f"Text processing error: {str(e)}"

def process_pdf_file(file_data: bytes) -> str:
    """Process PDF file with OCR fallback for image-based PDFs"""
    try:
        # First try direct text extraction
        with pdfplumber.open(io.BytesIO(file_data)) as pdf:
            text = ""
            for page in pdf.pages:
                page_text = page.extract_text()
                if page_text:
                    text += page_text + "\n"

            # If we got meaningful text, return it
            if text.strip() and len(text.strip()) > 10:
                return text

            # If no text or very little text, try OCR on the PDF pages
            try:
                import pdf2image
                # Convert PDF pages to images
                images = pdf2image.convert_from_bytes(file_data)
                ocr_text = ""

                for i, image in enumerate(images):
                    page_text = pytesseract.image_to_string(image)
                    if page_text.strip():
                        ocr_text += f"Page {i+1}:\n{page_text}\n\n"

                return ocr_text if ocr_text.strip() else "No text found in PDF (tried both text extraction and OCR)"

            except ImportError:
                return text if text.strip() else "No text found in PDF (OCR fallback not available)"
            except Exception as ocr_error:
                return text if text.strip() else f"No text found in PDF (OCR failed: {str(ocr_error)})"

    except Exception as e:
        return f"PDF processing error: {str(e)}"

def process_image_file(file_data: bytes) -> str:
    """Process image file with OCR"""
    try:
        image = Image.open(io.BytesIO(file_data))
        text = pytesseract.image_to_string(image)
        return text if text.strip() else "No text found in image"
    except Exception as e:
        return f"OCR processing error: {str(e)}"

def process_excel_file(file_data: bytes) -> str:
    """Process Excel file"""
    try:
        df = pd.read_excel(io.BytesIO(file_data))
        return df.to_string()
    except Exception as e:
        return f"Excel processing error: {str(e)}"

def process_word_file(file_data: bytes) -> str:
    """Process Word document"""
    try:
        doc = Document(io.BytesIO(file_data))
        text = ""
        for paragraph in doc.paragraphs:
            text += paragraph.text + "\n"
        return text if text.strip() else "No text found in document"
    except Exception as e:
        return f"Word processing error: {str(e)}"

def process_html_file(file_data: bytes) -> str:
    """Process HTML file"""
    try:
        html_content = file_data.decode('utf-8', errors='replace')
        soup = BeautifulSoup(html_content, 'html.parser')
        text = soup.get_text()
        return clean_text(text)
    except Exception as e:
        return f"HTML processing error: {str(e)}"

def process_csv_file(file_data: bytes) -> str:
    """Process CSV file"""
    try:
        df = pd.read_csv(io.BytesIO(file_data))
        return df.to_string()
    except Exception as e:
        return f"CSV processing error: {str(e)}"

@app.get("/api/v1/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "2.0.0"
    }

@app.post("/api/v1/extract-text")
async def extract_text(file: UploadFile = File(...)):
    """Main text extraction endpoint"""
    start_time = time.time()
    
    try:
        # Read file data
        file_data = await file.read()
        filename = file.filename or "unknown"
        file_type = detect_file_type(filename)
        
        # Process based on file type
        if file_type == 'txt':
            extracted_text = process_text_file(file_data)
            method = "direct_text"
            
        elif file_type == 'pdf':
            extracted_text = process_pdf_file(file_data)
            method = "pdf_extraction"
            
        elif file_type in ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'tiff']:
            extracted_text = process_image_file(file_data)
            method = "ocr"
            
        elif file_type in ['xlsx', 'xls']:
            extracted_text = process_excel_file(file_data)
            method = "excel_extraction"
            
        elif file_type == 'docx':
            extracted_text = process_word_file(file_data)
            method = "word_extraction"
            
        elif file_type in ['html', 'htm']:
            extracted_text = process_html_file(file_data)
            method = "html_extraction"
            
        elif file_type == 'csv':
            extracted_text = process_csv_file(file_data)
            method = "csv_extraction"
            
        else:
            extracted_text = f"Unsupported file type: {file_type}"
            method = "unsupported"

        # Clean the extracted text
        extracted_text = clean_text(extracted_text)
        
        # Calculate processing time
        processing_time = (time.time() - start_time) * 1000
        
        # Create response
        response = {
            "success": True,
            "extracted_text": extracted_text,
            "metadata": {
                "filename": filename,
                "file_type": file_type,
                "file_size": len(file_data),
                "extraction_method": method,
                "processing_time_ms": round(processing_time, 2),
                "text_length": len(extracted_text),
                "timestamp": datetime.now().isoformat()
            }
        }
        
        return response
        
    except Exception as e:
        processing_time = (time.time() - start_time) * 1000
        
        return {
            "success": False,
            "error": str(e),
            "metadata": {
                "filename": filename if 'filename' in locals() else "unknown",
                "processing_time_ms": round(processing_time, 2),
                "timestamp": datetime.now().isoformat()
            }
        }

@app.get("/api/v1/config")
async def get_config():
    """Get API configuration"""
    return {
        "supported_formats": [
            "txt", "pdf", "png", "jpg", "jpeg", "gif", "bmp", "tiff",
            "xlsx", "xls", "docx", "html", "htm", "csv"
        ],
        "max_file_size": "10MB",
        "version": "2.0.0",
        "features": {
            "text_extraction": True,
            "pdf_processing": True,
            "ocr_processing": True,
            "office_documents": True,
            "web_content": True,
            "structured_data": True
        }
    }

if __name__ == "__main__":
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info"
    )
